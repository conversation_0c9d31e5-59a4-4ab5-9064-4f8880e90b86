# Time Tracking Backend API

A C# REST API for integrating with Azure DevOps to retrieve project and work item information.

## Features

- **GET /api/projects** - Retrieve all Azure DevOps projects
- **GET /api/projects/{projectId}/workitems** - Retrieve work items for a specific project
- Comprehensive error handling and validation
- Swagger/OpenAPI documentation
- Azure DevOps REST API integration with authentication

## Prerequisites

- .NET 8.0 SDK
- Azure DevOps organization with Personal Access Token (PAT)
- Visual Studio 2022 or VS Code (optional)

## Configuration

1. Update `appsettings.json` with your Azure DevOps configuration:

```json
{
  "AzureDevOps": {
    "OrganizationUrl": "https://dev.azure.com/your-organization",
    "PersonalAccessToken": "your-pat-token-here",
    "ApiVersion": "7.0"
  }
}
```

2. To create a Personal Access Token:
   - Go to Azure DevOps → User Settings → Personal Access Tokens
   - Create a new token with "Work Items (Read)" and "Project and Team (Read)" permissions

## Running the Application

1. Clone the repository
2. Navigate to the project directory
3. Restore dependencies:
   ```bash
   dotnet restore
   ```
4. Run the application:
   ```bash
   dotnet run
   ```
5. Open your browser to `https://localhost:5001` to access Swagger UI

## API Endpoints

### GET /api/projects

Retrieves all projects from Azure DevOps.

**Response:**
```json
[
  {
    "id": "project-guid",
    "name": "Project Name",
    "description": "Project description",
    "state": "wellFormed",
    "visibility": "private",
    "url": "https://dev.azure.com/org/_apis/projects/project-guid"
  }
]
```

### GET /api/projects/{projectId}/workitems

Retrieves all work items for a specific project.

**Parameters:**
- `projectId` (string): The project identifier

**Response:**
```json
[
  {
    "id": 123,
    "title": "Work item title",
    "workItemType": "Task",
    "state": "Active",
    "assignedTo": "John Doe",
    "createdDate": "2024-01-01T10:00:00Z",
    "changedDate": "2024-01-02T15:30:00Z",
    "url": "https://dev.azure.com/org/project/_apis/wit/workItems/123"
  }
]
```

## Error Handling

The API includes comprehensive error handling:

- **400 Bad Request**: Invalid input parameters
- **404 Not Found**: Project not found
- **500 Internal Server Error**: Unexpected server errors
- **502 Bad Gateway**: Azure DevOps API communication errors

## Project Structure

```
TimeTrackingBackend/
├── Controllers/
│   └── ProjectsController.cs
├── Models/
│   ├── ProjectDto.cs
│   └── WorkItemDto.cs
├── Services/
│   ├── IAzureDevOpsService.cs
│   └── AzureDevOpsService.cs
├── Configuration/
│   └── AzureDevOpsSettings.cs
├── Middleware/
│   └── GlobalExceptionHandlerMiddleware.cs
├── Validation/
│   └── ValidationAttributes.cs
├── Program.cs
└── appsettings.json
```

## Development

To add new features or modify existing ones:

1. Follow the existing patterns for dependency injection
2. Add appropriate logging using `ILogger<T>`
3. Include proper error handling and validation
4. Update API documentation comments
5. Test endpoints using Swagger UI or your preferred API testing tool

## Security Considerations

- Store Personal Access Tokens securely (use Azure Key Vault in production)
- Implement proper authentication/authorization for your API endpoints
- Consider rate limiting for production deployments
- Use HTTPS in production environments

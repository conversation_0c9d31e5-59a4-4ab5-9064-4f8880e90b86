# Use the official .NET 8 runtime image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 5000
EXPOSE 5001

# Use the official .NET 8 SDK image for building
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["TimeTrackingBackend.csproj", "."]
RUN dotnet restore "TimeTrackingBackend.csproj"
COPY . .
WORKDIR "/src"
RUN dotnet build "TimeTrackingBackend.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "TimeTrackingBackend.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

# Set environment to Production
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:5000;https://+:5001

ENTRYPOINT ["dotnet", "TimeTrackingBackend.dll"]

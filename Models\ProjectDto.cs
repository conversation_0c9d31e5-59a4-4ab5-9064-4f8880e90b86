using System.Text.Json.Serialization;

namespace TimeTrackingBackend.Models;

/// <summary>
/// Data transfer object for Azure DevOps project information
/// </summary>
public class ProjectDto
{
    /// <summary>
    /// Unique identifier for the project
    /// </summary>
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Display name of the project
    /// </summary>
    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Project description
    /// </summary>
    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Current status of the project (e.g., wellFormed, createPending, etc.)
    /// </summary>
    [JsonPropertyName("state")]
    public string State { get; set; } = string.Empty;

    /// <summary>
    /// Project visibility (private, public)
    /// </summary>
    [JsonPropertyName("visibility")]
    public string Visibility { get; set; } = string.Empty;

    /// <summary>
    /// URL to the project
    /// </summary>
    [JsonPropertyName("url")]
    public string Url { get; set; } = string.Empty;
}

/// <summary>
/// Response wrapper for Azure DevOps projects API
/// </summary>
public class ProjectsResponse
{
    /// <summary>
    /// List of projects
    /// </summary>
    [JsonPropertyName("value")]
    public List<ProjectDto> Value { get; set; } = new();

    /// <summary>
    /// Total count of projects
    /// </summary>
    [JsonPropertyName("count")]
    public int Count { get; set; }
}

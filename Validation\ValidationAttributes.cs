using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace TimeTrackingBackend.Validation;

/// <summary>
/// Custom validation attribute for Azure DevOps project IDs
/// </summary>
public class ProjectIdValidationAttribute : ValidationAttribute
{
    private static readonly Regex ProjectIdRegex = new(@"^[a-zA-Z0-9\-_\.]{1,64}$", RegexOptions.Compiled);

    public override bool IsValid(object? value)
    {
        if (value is null)
            return false;

        if (value is not string projectId)
            return false;

        if (string.IsNullOrWhiteSpace(projectId))
            return false;

        // Check length
        if (projectId.Length > 64)
            return false;

        // Check for invalid characters or patterns
        if (projectId.Contains("..") || projectId.Contains("//"))
            return false;

        // Check against regex pattern
        return ProjectIdRegex.IsMatch(projectId);
    }

    public override string FormatErrorMessage(string name)
    {
        return $"The {name} field must be a valid project identifier (1-64 characters, alphanumeric, hyphens, underscores, and dots only).";
    }
}

/// <summary>
/// Custom validation attribute for ensuring a string is not empty or whitespace
/// </summary>
public class NotEmptyOrWhitespaceAttribute : ValidationAttribute
{
    public override bool IsValid(object? value)
    {
        if (value is null)
            return false;

        if (value is string stringValue)
            return !string.IsNullOrWhiteSpace(stringValue);

        return true;
    }

    public override string FormatErrorMessage(string name)
    {
        return $"The {name} field cannot be empty or contain only whitespace.";
    }
}

using TimeTrackingBackend.Services;
using TimeTrackingBackend.Configuration;
using TimeTrackingBackend.Middleware;
using Microsoft.OpenApi.Models;
using System.Reflection;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();

// Configure Swagger/OpenAPI
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Time Tracking Backend API",
        Version = "v1",
        Description = "REST API for integrating with Azure DevOps projects and work items",
        Contact = new OpenApiContact
        {
            Name = "Development Team",
            Email = "<EMAIL>"
        }
    });

    // Include XML comments for API documentation
    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

// Configure Azure DevOps settings
builder.Services.Configure<AzureDevOpsSettings>(
    builder.Configuration.GetSection("AzureDevOps"));

// Validate Azure DevOps configuration
var azureDevOpsConfig = builder.Configuration.GetSection("AzureDevOps");
if (string.IsNullOrEmpty(azureDevOpsConfig["OrganizationUrl"]) ||
    string.IsNullOrEmpty(azureDevOpsConfig["PersonalAccessToken"]))
{
    throw new InvalidOperationException(
        "Azure DevOps configuration is missing. Please ensure OrganizationUrl and PersonalAccessToken are configured.");
}

// Add HTTP client for Azure DevOps API with timeout configuration
builder.Services.AddHttpClient<IAzureDevOpsService, AzureDevOpsService>(client =>
{
    client.Timeout = TimeSpan.FromSeconds(30);
});

// Register Azure DevOps service
builder.Services.AddScoped<IAzureDevOpsService, AzureDevOpsService>();

// Add logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.AddDebug();

var app = builder.Build();

// Configure the HTTP request pipeline.
// Add global exception handling middleware first
app.UseMiddleware<GlobalExceptionHandlerMiddleware>();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Time Tracking Backend API v1");
        c.RoutePrefix = string.Empty; // Set Swagger UI at the app's root
    });
}

app.UseHttpsRedirection();

app.UseAuthorization();

app.MapControllers();

app.Run();

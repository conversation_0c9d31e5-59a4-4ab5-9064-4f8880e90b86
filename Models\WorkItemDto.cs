using System.Text.Json.Serialization;

namespace TimeTrackingBackend.Models;

/// <summary>
/// Data transfer object for Azure DevOps work item information
/// </summary>
public class WorkItemDto
{
    /// <summary>
    /// Unique identifier for the work item
    /// </summary>
    [JsonPropertyName("id")]
    public int Id { get; set; }

    /// <summary>
    /// Title of the work item
    /// </summary>
    [JsonPropertyName("title")]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Type of work item (Bug, Task, User Story, etc.)
    /// </summary>
    [JsonPropertyName("workItemType")]
    public string WorkItemType { get; set; } = string.Empty;

    /// <summary>
    /// Current state of the work item (New, Active, Resolved, Closed, etc.)
    /// </summary>
    [JsonPropertyName("state")]
    public string State { get; set; } = string.Empty;

    /// <summary>
    /// User assigned to the work item
    /// </summary>
    [JsonPropertyName("assignedTo")]
    public string? AssignedTo { get; set; }

    /// <summary>
    /// Date when the work item was created
    /// </summary>
    [JsonPropertyName("createdDate")]
    public DateTime CreatedDate { get; set; }

    /// <summary>
    /// Date when the work item was last changed
    /// </summary>
    [JsonPropertyName("changedDate")]
    public DateTime ChangedDate { get; set; }

    /// <summary>
    /// URL to the work item
    /// </summary>
    [JsonPropertyName("url")]
    public string Url { get; set; } = string.Empty;
}

/// <summary>
/// Response wrapper for Azure DevOps work items API
/// </summary>
public class WorkItemsResponse
{
    /// <summary>
    /// List of work items
    /// </summary>
    [JsonPropertyName("value")]
    public List<WorkItemDto> Value { get; set; } = new();

    /// <summary>
    /// Total count of work items
    /// </summary>
    [JsonPropertyName("count")]
    public int Count { get; set; }
}

/// <summary>
/// Internal model for Azure DevOps work item fields
/// </summary>
public class AzureDevOpsWorkItem
{
    [JsonPropertyName("id")]
    public int Id { get; set; }

    [JsonPropertyName("url")]
    public string Url { get; set; } = string.Empty;

    [JsonPropertyName("fields")]
    public WorkItemFields Fields { get; set; } = new();
}

/// <summary>
/// Work item fields from Azure DevOps API
/// </summary>
public class WorkItemFields
{
    [JsonPropertyName("System.Title")]
    public string? Title { get; set; }

    [JsonPropertyName("System.WorkItemType")]
    public string? WorkItemType { get; set; }

    [JsonPropertyName("System.State")]
    public string? State { get; set; }

    [JsonPropertyName("System.AssignedTo")]
    public AssignedToField? AssignedTo { get; set; }

    [JsonPropertyName("System.CreatedDate")]
    public DateTime? CreatedDate { get; set; }

    [JsonPropertyName("System.ChangedDate")]
    public DateTime? ChangedDate { get; set; }
}

/// <summary>
/// Assigned to field structure from Azure DevOps
/// </summary>
public class AssignedToField
{
    [JsonPropertyName("displayName")]
    public string? DisplayName { get; set; }

    [JsonPropertyName("uniqueName")]
    public string? UniqueName { get; set; }
}

/// <summary>
/// Detailed work item DTO with expanded information
/// </summary>
public class WorkItemDetailDto
{
    /// <summary>
    /// Unique identifier for the work item
    /// </summary>
    [JsonPropertyName("id")]
    public int Id { get; set; }

    /// <summary>
    /// Title of the work item
    /// </summary>
    [JsonPropertyName("title")]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Type of work item (Bug, Task, User Story, etc.)
    /// </summary>
    [JsonPropertyName("workItemType")]
    public string WorkItemType { get; set; } = string.Empty;

    /// <summary>
    /// Current state of the work item
    /// </summary>
    [JsonPropertyName("state")]
    public string State { get; set; } = string.Empty;

    /// <summary>
    /// User assigned to the work item
    /// </summary>
    [JsonPropertyName("assignedTo")]
    public string? AssignedTo { get; set; }

    /// <summary>
    /// User who created the work item
    /// </summary>
    [JsonPropertyName("createdBy")]
    public string? CreatedBy { get; set; }

    /// <summary>
    /// Date when the work item was created
    /// </summary>
    [JsonPropertyName("createdDate")]
    public DateTime CreatedDate { get; set; }

    /// <summary>
    /// Date when the work item was last changed
    /// </summary>
    [JsonPropertyName("changedDate")]
    public DateTime ChangedDate { get; set; }

    /// <summary>
    /// User who last changed the work item
    /// </summary>
    [JsonPropertyName("changedBy")]
    public string? ChangedBy { get; set; }

    /// <summary>
    /// Description of the work item
    /// </summary>
    [JsonPropertyName("description")]
    public string? Description { get; set; }

    /// <summary>
    /// Acceptance criteria
    /// </summary>
    [JsonPropertyName("acceptanceCriteria")]
    public string? AcceptanceCriteria { get; set; }

    /// <summary>
    /// Priority of the work item
    /// </summary>
    [JsonPropertyName("priority")]
    public int? Priority { get; set; }

    /// <summary>
    /// Severity of the work item (for bugs)
    /// </summary>
    [JsonPropertyName("severity")]
    public string? Severity { get; set; }

    /// <summary>
    /// Story points or effort estimation
    /// </summary>
    [JsonPropertyName("storyPoints")]
    public double? StoryPoints { get; set; }

    /// <summary>
    /// Original estimate in hours
    /// </summary>
    [JsonPropertyName("originalEstimate")]
    public double? OriginalEstimate { get; set; }

    /// <summary>
    /// Remaining work in hours
    /// </summary>
    [JsonPropertyName("remainingWork")]
    public double? RemainingWork { get; set; }

    /// <summary>
    /// Completed work in hours
    /// </summary>
    [JsonPropertyName("completedWork")]
    public double? CompletedWork { get; set; }

    /// <summary>
    /// Tags associated with the work item
    /// </summary>
    [JsonPropertyName("tags")]
    public string? Tags { get; set; }

    /// <summary>
    /// Area path
    /// </summary>
    [JsonPropertyName("areaPath")]
    public string? AreaPath { get; set; }

    /// <summary>
    /// Iteration path
    /// </summary>
    [JsonPropertyName("iterationPath")]
    public string? IterationPath { get; set; }

    /// <summary>
    /// URL to the work item
    /// </summary>
    [JsonPropertyName("url")]
    public string Url { get; set; } = string.Empty;

    /// <summary>
    /// Related work items (links)
    /// </summary>
    [JsonPropertyName("relations")]
    public List<WorkItemRelation>? Relations { get; set; }
}

/// <summary>
/// Work item relation/link information
/// </summary>
public class WorkItemRelation
{
    /// <summary>
    /// Type of relation (e.g., "System.LinkTypes.Hierarchy-Forward", "System.LinkTypes.Related")
    /// </summary>
    [JsonPropertyName("rel")]
    public string Rel { get; set; } = string.Empty;

    /// <summary>
    /// URL to the related work item
    /// </summary>
    [JsonPropertyName("url")]
    public string Url { get; set; } = string.Empty;

    /// <summary>
    /// Attributes of the relation
    /// </summary>
    [JsonPropertyName("attributes")]
    public Dictionary<string, object>? Attributes { get; set; }
}

/// <summary>
/// Request model for updating a work item
/// </summary>
public class WorkItemUpdateRequest
{
    /// <summary>
    /// Title of the work item
    /// </summary>
    [JsonPropertyName("title")]
    public string? Title { get; set; }

    /// <summary>
    /// State of the work item (New, Active, Resolved, Closed, etc.)
    /// </summary>
    [JsonPropertyName("state")]
    public string? State { get; set; }

    /// <summary>
    /// User assigned to the work item (email or display name)
    /// </summary>
    [JsonPropertyName("assignedTo")]
    public string? AssignedTo { get; set; }

    /// <summary>
    /// Description of the work item
    /// </summary>
    [JsonPropertyName("description")]
    public string? Description { get; set; }

    /// <summary>
    /// Acceptance criteria
    /// </summary>
    [JsonPropertyName("acceptanceCriteria")]
    public string? AcceptanceCriteria { get; set; }

    /// <summary>
    /// Priority of the work item
    /// </summary>
    [JsonPropertyName("priority")]
    public int? Priority { get; set; }

    /// <summary>
    /// Story points or effort estimation
    /// </summary>
    [JsonPropertyName("storyPoints")]
    public double? StoryPoints { get; set; }

    /// <summary>
    /// Original estimate in hours
    /// </summary>
    [JsonPropertyName("originalEstimate")]
    public double? OriginalEstimate { get; set; }

    /// <summary>
    /// Remaining work in hours
    /// </summary>
    [JsonPropertyName("remainingWork")]
    public double? RemainingWork { get; set; }

    /// <summary>
    /// Completed work in hours
    /// </summary>
    [JsonPropertyName("completedWork")]
    public double? CompletedWork { get; set; }

    /// <summary>
    /// Tags associated with the work item (semicolon separated)
    /// </summary>
    [JsonPropertyName("tags")]
    public string? Tags { get; set; }

    /// <summary>
    /// Area path
    /// </summary>
    [JsonPropertyName("areaPath")]
    public string? AreaPath { get; set; }

    /// <summary>
    /// Iteration path
    /// </summary>
    [JsonPropertyName("iterationPath")]
    public string? IterationPath { get; set; }
}

/// <summary>
/// Simplified request model for updating just the completed work field
/// </summary>
public class CompletedWorkUpdateRequest
{
    /// <summary>
    /// Completed work in hours
    /// </summary>
    [JsonPropertyName("completedWork")]
    public double CompletedWork { get; set; }

    /// <summary>
    /// Optional: Remaining work in hours (will be calculated automatically if not provided)
    /// </summary>
    [JsonPropertyName("remainingWork")]
    public double? RemainingWork { get; set; }
}

/// <summary>
/// Internal model for Azure DevOps work item update operations
/// </summary>
public class AzureDevOpsUpdateOperation
{
    /// <summary>
    /// Operation type (add, replace, remove, test, move, copy)
    /// </summary>
    [JsonPropertyName("op")]
    public string Op { get; set; } = string.Empty;

    /// <summary>
    /// Path to the field being updated (e.g., "/fields/System.Title")
    /// </summary>
    [JsonPropertyName("path")]
    public string Path { get; set; } = string.Empty;

    /// <summary>
    /// Value to set for the field
    /// </summary>
    [JsonPropertyName("value")]
    public object? Value { get; set; }

    /// <summary>
    /// Previous value (used for test operations)
    /// </summary>
    [JsonPropertyName("from")]
    public string? From { get; set; }
}

/// <summary>
/// Response wrapper for Azure DevOps work items query API
/// </summary>
public class AzureDevOpsWorkItemsResponse
{
    [JsonPropertyName("value")]
    public List<AzureDevOpsWorkItem> Value { get; set; } = new();

    [JsonPropertyName("count")]
    public int Count { get; set; }
}

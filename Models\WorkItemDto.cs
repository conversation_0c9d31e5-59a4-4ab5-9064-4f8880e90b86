using System.Text.Json.Serialization;

namespace TimeTrackingBackend.Models;

/// <summary>
/// Data transfer object for Azure DevOps work item information
/// </summary>
public class WorkItemDto
{
    /// <summary>
    /// Unique identifier for the work item
    /// </summary>
    [JsonPropertyName("id")]
    public int Id { get; set; }

    /// <summary>
    /// Title of the work item
    /// </summary>
    [JsonPropertyName("title")]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Type of work item (Bug, Task, User Story, etc.)
    /// </summary>
    [JsonPropertyName("workItemType")]
    public string WorkItemType { get; set; } = string.Empty;

    /// <summary>
    /// Current state of the work item (New, Active, Resolved, Closed, etc.)
    /// </summary>
    [JsonPropertyName("state")]
    public string State { get; set; } = string.Empty;

    /// <summary>
    /// User assigned to the work item
    /// </summary>
    [JsonPropertyName("assignedTo")]
    public string? AssignedTo { get; set; }

    /// <summary>
    /// Date when the work item was created
    /// </summary>
    [JsonPropertyName("createdDate")]
    public DateTime CreatedDate { get; set; }

    /// <summary>
    /// Date when the work item was last changed
    /// </summary>
    [JsonPropertyName("changedDate")]
    public DateTime ChangedDate { get; set; }

    /// <summary>
    /// URL to the work item
    /// </summary>
    [JsonPropertyName("url")]
    public string Url { get; set; } = string.Empty;
}

/// <summary>
/// Response wrapper for Azure DevOps work items API
/// </summary>
public class WorkItemsResponse
{
    /// <summary>
    /// List of work items
    /// </summary>
    [JsonPropertyName("value")]
    public List<WorkItemDto> Value { get; set; } = new();

    /// <summary>
    /// Total count of work items
    /// </summary>
    [JsonPropertyName("count")]
    public int Count { get; set; }
}

/// <summary>
/// Internal model for Azure DevOps work item fields
/// </summary>
public class AzureDevOpsWorkItem
{
    [JsonPropertyName("id")]
    public int Id { get; set; }

    [JsonPropertyName("url")]
    public string Url { get; set; } = string.Empty;

    [JsonPropertyName("fields")]
    public WorkItemFields Fields { get; set; } = new();
}

/// <summary>
/// Work item fields from Azure DevOps API
/// </summary>
public class WorkItemFields
{
    [JsonPropertyName("System.Title")]
    public string? Title { get; set; }

    [JsonPropertyName("System.WorkItemType")]
    public string? WorkItemType { get; set; }

    [JsonPropertyName("System.State")]
    public string? State { get; set; }

    [JsonPropertyName("System.AssignedTo")]
    public AssignedToField? AssignedTo { get; set; }

    [JsonPropertyName("System.CreatedDate")]
    public DateTime? CreatedDate { get; set; }

    [JsonPropertyName("System.ChangedDate")]
    public DateTime? ChangedDate { get; set; }
}

/// <summary>
/// Assigned to field structure from Azure DevOps
/// </summary>
public class AssignedToField
{
    [JsonPropertyName("displayName")]
    public string? DisplayName { get; set; }

    [JsonPropertyName("uniqueName")]
    public string? UniqueName { get; set; }
}

/// <summary>
/// Response wrapper for Azure DevOps work items query API
/// </summary>
public class AzureDevOpsWorkItemsResponse
{
    [JsonPropertyName("value")]
    public List<AzureDevOpsWorkItem> Value { get; set; } = new();

    [JsonPropertyName("count")]
    public int Count { get; set; }
}

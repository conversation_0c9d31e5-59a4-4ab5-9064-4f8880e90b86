version: '3.8'

services:
  timetracking-backend:
    build: .
    container_name: timetracking-backend
    ports:
      - "5000:5000"
      - "5001:5001"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:5000;https://+:5001
    volumes:
      - ./appsettings.Production.json:/app/appsettings.Production.json:ro
    restart: unless-stopped
    networks:
      - timetracking-network

networks:
  timetracking-network:
    driver: bridge

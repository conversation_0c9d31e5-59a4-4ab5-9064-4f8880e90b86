{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "AzureDevOps": {"OrganizationUrl": "https://core-fin.visualstudio.com", "ApiVersion": "7.0", "PersonalAccessToken": "5X8bKUB9JywT7dMPWtN9LlfjD5lu8826KUL4MnCNPbxZ4hzHgvPCJQQJ99BIACAAAAAiIW16AAASAZDO2IXR", "Authentication": {"Type": "PAT"}}, "Kestrel": {"Endpoints": {"Http": {"Url": "http://127.0.0.1:5000"}}}, "ForwardedHeaders": {"ForwardedProtoHeaderName": "X-Forwarded-Proto", "ForwardedForHeaderName": "X-Forwarded-For", "ForwardedHostHeaderName": "X-Forwarded-Host"}}
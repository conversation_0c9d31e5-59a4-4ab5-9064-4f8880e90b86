# PowerShell deployment script for Windows VM

param(
    [string]$TargetPath = "C:\Apps\TimeTrackingBackend",
    [string]$ServiceName = "TimeTrackingBackend"
)

Write-Host "Starting deployment of Time Tracking Backend API..." -ForegroundColor Green

# Stop the service if it exists
if (Get-Service -Name $ServiceName -ErrorAction SilentlyContinue) {
    Write-Host "Stopping existing service..." -ForegroundColor Yellow
    Stop-Service -Name $ServiceName -Force
    Start-Sleep -Seconds 5
}

# Create target directory if it doesn't exist
if (!(Test-Path -Path $TargetPath)) {
    Write-Host "Creating target directory: $TargetPath" -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $TargetPath -Force
}

# Build and publish the application
Write-Host "Building application..." -ForegroundColor Yellow
dotnet clean
dotnet publish --configuration Release --output $TargetPath --runtime win-x64

# Copy additional files
Write-Host "Copying configuration files..." -ForegroundColor Yellow
Copy-Item "appsettings.Production.json" -Destination $TargetPath -Force

# Create Windows Service configuration
$serviceConfig = @"
[Unit]
Description=Time Tracking Backend API
After=network.target

[Service]
Type=notify
ExecStart=$TargetPath\TimeTrackingBackend.exe
Restart=always
RestartSec=10
KillSignal=SIGINT
SyslogIdentifier=timetracking-backend
User=NetworkService
Environment=ASPNETCORE_ENVIRONMENT=Production
Environment=DOTNET_PRINT_TELEMETRY_MESSAGE=false

[Install]
WantedBy=multi-user.target
"@

# For Windows, we'll use NSSM (Non-Sucking Service Manager) or sc command
Write-Host "Service configuration created. Use NSSM or sc command to install as Windows service." -ForegroundColor Green

# Set permissions
Write-Host "Setting permissions..." -ForegroundColor Yellow
icacls $TargetPath /grant "IIS_IUSRS:(OI)(CI)F" /T

Write-Host "Deployment completed successfully!" -ForegroundColor Green
Write-Host "Application deployed to: $TargetPath" -ForegroundColor Cyan
Write-Host "To run manually: cd $TargetPath && .\TimeTrackingBackend.exe" -ForegroundColor Cyan

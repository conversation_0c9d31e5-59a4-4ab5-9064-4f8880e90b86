{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "AzureDevOps": {
    "OrganizationUrl": "https://dev.azure.com/your-organization-name",
    "ApiVersion": "7.0",
    
    // Option 1: OAuth Client Credentials (Recommended for production)
    "Authentication": {
      "Type": "ClientCredentials",
      "TenantId": "your-azure-ad-tenant-id",
      "ClientId": "your-app-registration-client-id", 
      "ClientSecret": "your-app-registration-client-secret",
      "Scope": "https://app.vssps.visualstudio.com/.default"
    }
    
    // Option 2: Personal Access Token (Alternative method)
    // "PersonalAccessToken": "your-personal-access-token",
    // "Authentication": {
    //   "Type": "PAT"
    // }
  }
}

{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "AzureDevOps": {
    "OrganizationUrl": "https://dev.azure.com/your-organization-name",
    "ApiVersion": "7.0",
    
    // Option 1: OAuth Client Credentials (Recommended for production)
    "Authentication": {
      "Type": "ClientCredentials",
      "TenantId": "de52169b-5ab2-45ec-b248-a7ea2f5f2637",
      "ClientId": "ccc7b049-0510-467c-921f-400ab9aeabd7", 
      "ClientSecret": "****************************************",
      "Scope": "https://app.vssps.visualstudio.com/.default"
    }
    
    // Option 2: Personal Access Token (Alternative method)
    // "PersonalAccessToken": "your-personal-access-token",
    // "Authentication": {
    //   "Type": "PAT"
    // }
  }
}

using System.Net;
using System.Text.Json;

namespace TimeTrackingBackend.Middleware;

/// <summary>
/// Global exception handling middleware to catch and handle unhandled exceptions
/// </summary>
public class GlobalExceptionHandlerMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalExceptionHandlerMiddleware> _logger;

    public GlobalExceptionHandlerMiddleware(RequestDelegate next, ILogger<GlobalExceptionHandlerMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unhandled exception occurred while processing the request");
            await HandleExceptionAsync(context, ex);
        }
    }

    private static async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        context.Response.ContentType = "application/json";

        var response = new
        {
            title = "An error occurred",
            status = (int)HttpStatusCode.InternalServerError,
            detail = "An unexpected error occurred while processing your request.",
            traceId = context.TraceIdentifier
        };

        switch (exception)
        {
            case HttpRequestException httpEx:
                response = new
                {
                    title = "External Service Error",
                    status = (int)HttpStatusCode.BadGateway,
                    detail = "Failed to communicate with external service. Please try again later.",
                    traceId = context.TraceIdentifier
                };
                context.Response.StatusCode = (int)HttpStatusCode.BadGateway;
                break;

            case ArgumentException argEx:
                response = new
                {
                    title = "Invalid Argument",
                    status = (int)HttpStatusCode.BadRequest,
                    detail = argEx.Message,
                    traceId = context.TraceIdentifier
                };
                context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                break;

            case UnauthorizedAccessException:
                response = new
                {
                    title = "Unauthorized",
                    status = (int)HttpStatusCode.Unauthorized,
                    detail = "You are not authorized to access this resource.",
                    traceId = context.TraceIdentifier
                };
                context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                break;

            default:
                context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                break;
        }

        var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        await context.Response.WriteAsync(jsonResponse);
    }
}

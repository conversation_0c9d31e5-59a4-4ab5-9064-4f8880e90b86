<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath=".\TimeTrackingBackend.exe" 
                  stdoutLogEnabled="false" 
                  stdoutLogFile=".\logs\stdout" 
                  hostingModel="outofprocess">
        <environmentVariables>
          <environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Production" />
          <environmentVariable name="ASPNETCORE_URLS" value="http://127.0.0.1:5000" />
        </environmentVariables>
      </aspNetCore>
      
      <!-- URL Rewrite rules for path base -->
      <rewrite>
        <rules>
          <rule name="TimeTracking API" stopProcessing="true">
            <match url="^TimeTracking/(.*)" />
            <action type="Rewrite" url="http://127.0.0.1:5000/{R:1}" />
            <serverVariables>
              <set name="HTTP_X_FORWARDED_PROTO" value="https" />
              <set name="HTTP_X_FORWARDED_HOST" value="{HTTP_HOST}" />
              <set name="HTTP_X_FORWARDED_FOR" value="{REMOTE_ADDR}" />
            </serverVariables>
          </rule>
        </rules>
      </rewrite>
      
      <!-- Security headers -->
      <httpProtocol>
        <customHeaders>
          <add name="X-Content-Type-Options" value="nosniff" />
          <add name="X-Frame-Options" value="DENY" />
          <add name="X-XSS-Protection" value="1; mode=block" />
        </customHeaders>
      </httpProtocol>
    </system.webServer>
  </location>
</configuration>

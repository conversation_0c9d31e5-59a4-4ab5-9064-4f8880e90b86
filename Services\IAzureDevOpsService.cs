using TimeTrackingBackend.Models;

namespace TimeTrackingBackend.Services;

/// <summary>
/// Interface for Azure DevOps service operations
/// </summary>
public interface IAzureDevOpsService
{
    /// <summary>
    /// Retrieves all projects from Azure DevOps
    /// </summary>
    /// <returns>List of projects</returns>
    Task<IEnumerable<ProjectDto>> GetProjectsAsync();

    /// <summary>
    /// Retrieves all work items for a specific project
    /// </summary>
    /// <param name="projectId">The project identifier</param>
    /// <returns>List of work items for the project</returns>
    Task<IEnumerable<WorkItemDto>> GetWorkItemsAsync(string projectId);

    /// <summary>
    /// Retrieves a specific work item with all expanded details
    /// </summary>
    /// <param name="workItemId">The work item identifier</param>
    /// <returns>Detailed work item information</returns>
    Task<WorkItemDetailDto> GetWorkItemAsync(int workItemId);
}

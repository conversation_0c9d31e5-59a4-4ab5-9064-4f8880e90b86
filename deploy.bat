@echo off
echo Starting deployment of Time Tracking Backend API...

REM Set deployment path
set DEPLOY_PATH=C:\Apps\TimeTrackingBackend

REM Create directory if it doesn't exist
if not exist "%DEPLOY_PATH%" (
    echo Creating deployment directory...
    mkdir "%DEPLOY_PATH%"
)

REM Clean and build
echo Cleaning previous builds...
dotnet clean

echo Building for production...
dotnet publish --configuration Release --output "%DEPLOY_PATH%" --runtime win-x64

REM Copy production config
echo Copying production configuration...
copy "appsettings.Production.json" "%DEPLOY_PATH%\"

echo.
echo Deployment completed successfully!
echo Application deployed to: %DEPLOY_PATH%
echo.
echo To run the application:
echo cd "%DEPLOY_PATH%"
echo TimeTrackingBackend.exe
echo.
echo The API will be available at:
echo http://localhost:5000
echo https://localhost:5001
echo.
pause

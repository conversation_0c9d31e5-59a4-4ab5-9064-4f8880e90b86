namespace TimeTrackingBackend.Configuration;

/// <summary>
/// Configuration settings for Azure DevOps integration
/// </summary>
public class AzureDevOpsSettings
{
    /// <summary>
    /// The Azure DevOps organization URL (e.g., https://dev.azure.com/your-organization)
    /// </summary>
    public string OrganizationUrl { get; set; } = string.Empty;

    /// <summary>
    /// Personal Access Token for Azure DevOps authentication
    /// </summary>
    public string PersonalAccessToken { get; set; } = string.Empty;

    /// <summary>
    /// Azure DevOps REST API version
    /// </summary>
    public string ApiVersion { get; set; } = "7.0";
}

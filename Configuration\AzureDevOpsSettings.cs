namespace TimeTrackingBackend.Configuration;

/// <summary>
/// Configuration settings for Azure DevOps integration
/// </summary>
public class AzureDevOpsSettings
{
    /// <summary>
    /// The Azure DevOps organization URL (e.g., https://dev.azure.com/your-organization)
    /// </summary>
    public string OrganizationUrl { get; set; } = string.Empty;

    /// <summary>
    /// Personal Access Token for Azure DevOps authentication (legacy method)
    /// </summary>
    public string PersonalAccessToken { get; set; } = string.Empty;

    /// <summary>
    /// Azure DevOps REST API version
    /// </summary>
    public string ApiVersion { get; set; } = "7.0";

    /// <summary>
    /// Authentication configuration
    /// </summary>
    public AuthenticationSettings Authentication { get; set; } = new();
}

/// <summary>
/// Authentication settings for Azure DevOps
/// </summary>
public class AuthenticationSettings
{
    /// <summary>
    /// Authentication type: "PAT" for Personal Access Token or "ClientCredentials" for OAuth
    /// </summary>
    public string Type { get; set; } = "PAT";

    /// <summary>
    /// Azure AD Tenant ID (required for ClientCredentials)
    /// </summary>
    public string TenantId { get; set; } = string.Empty;

    /// <summary>
    /// Azure AD Application (Client) ID (required for ClientCredentials)
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// Azure AD Application Client Secret (required for ClientCredentials)
    /// </summary>
    public string ClientSecret { get; set; } = string.Empty;

    /// <summary>
    /// OAuth scope for Azure DevOps (typically https://app.vssps.visualstudio.com/.default)
    /// </summary>
    public string Scope { get; set; } = "https://app.vssps.visualstudio.com/.default";
}

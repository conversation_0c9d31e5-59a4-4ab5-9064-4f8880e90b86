using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using TimeTrackingBackend.Configuration;
using TimeTrackingBackend.Models;

namespace TimeTrackingBackend.Services;

/// <summary>
/// Service for interacting with Azure DevOps REST API
/// </summary>
public class AzureDevOpsService : IAzureDevOpsService
{
    private readonly HttpClient _httpClient;
    private readonly AzureDevOpsSettings _settings;
    private readonly ILogger<AzureDevOpsService> _logger;

    public AzureDevOpsService(
        HttpClient httpClient,
        IOptions<AzureDevOpsSettings> settings,
        ILogger<AzureDevOpsService> logger)
    {
        _httpClient = httpClient;
        _settings = settings.Value;
        _logger = logger;

        ConfigureHttpClient();
    }

    /// <summary>
    /// Configures the HTTP client with authentication and base settings
    /// </summary>
    private void ConfigureHttpClient()
    {
        // Set base address
        _httpClient.BaseAddress = new Uri(_settings.OrganizationUrl);

        // Set content type
        _httpClient.DefaultRequestHeaders.Accept.Clear();
        _httpClient.DefaultRequestHeaders.Accept.Add(
            new MediaTypeWithQualityHeaderValue("application/json"));

        // Configure authentication based on type
        if (_settings.Authentication.Type.Equals("ClientCredentials", StringComparison.OrdinalIgnoreCase))
        {
            // OAuth authentication will be handled per request
            _logger.LogInformation("Using OAuth Client Credentials authentication");
        }
        else
        {
            // Default to PAT authentication
            if (!string.IsNullOrEmpty(_settings.PersonalAccessToken))
            {
                var authToken = Convert.ToBase64String(
                    Encoding.ASCII.GetBytes($":{_settings.PersonalAccessToken}"));
                _httpClient.DefaultRequestHeaders.Authorization =
                    new AuthenticationHeaderValue("Basic", authToken);
                _logger.LogInformation("Using Personal Access Token authentication");
            }
            else
            {
                _logger.LogWarning("No authentication method configured");
            }
        }
    }

    /// <summary>
    /// Gets an access token for OAuth authentication
    /// </summary>
    private async Task<string?> GetAccessTokenAsync()
    {
        if (!_settings.Authentication.Type.Equals("ClientCredentials", StringComparison.OrdinalIgnoreCase))
            return null;

        try
        {
            var app = ConfidentialClientApplicationBuilder
                .Create(_settings.Authentication.ClientId)
                .WithClientSecret(_settings.Authentication.ClientSecret)
                .WithAuthority(new Uri($"https://login.microsoftonline.com/{_settings.Authentication.TenantId}"))
                .Build();

            var result = await app.AcquireTokenForClient(new[] { _settings.Authentication.Scope })
                .ExecuteAsync();

            return result.AccessToken;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to acquire access token");
            throw new UnauthorizedAccessException("Failed to authenticate with Azure AD", ex);
        }
    }

    /// <summary>
    /// Configures the request with appropriate authentication
    /// </summary>
    private async Task<HttpRequestMessage> CreateAuthenticatedRequestAsync(HttpMethod method, string requestUri)
    {
        var request = new HttpRequestMessage(method, requestUri);

        if (_settings.Authentication.Type.Equals("ClientCredentials", StringComparison.OrdinalIgnoreCase))
        {
            var token = await GetAccessTokenAsync();
            if (!string.IsNullOrEmpty(token))
            {
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
            }
        }
        // For PAT authentication, the header is already set in ConfigureHttpClient

        return request;
    }

    /// <summary>
    /// Retrieves all projects from Azure DevOps
    /// </summary>
    public async Task<IEnumerable<ProjectDto>> GetProjectsAsync()
    {
        try
        {
            _logger.LogInformation("Fetching projects from Azure DevOps");

            var requestUri = $"_apis/projects?api-version={_settings.ApiVersion}";

            HttpResponseMessage response;
            if (_settings.Authentication.Type.Equals("ClientCredentials", StringComparison.OrdinalIgnoreCase))
            {
                var request = await CreateAuthenticatedRequestAsync(HttpMethod.Get, requestUri);
                response = await _httpClient.SendAsync(request);
            }
            else
            {
                response = await _httpClient.GetAsync(requestUri);
            }

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to fetch projects. Status: {StatusCode}, Reason: {ReasonPhrase}",
                    response.StatusCode, response.ReasonPhrase);
                throw new HttpRequestException($"Azure DevOps API returned {response.StatusCode}: {response.ReasonPhrase}");
            }

            var content = await response.Content.ReadAsStringAsync();
            var projectsResponse = JsonSerializer.Deserialize<ProjectsResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            _logger.LogInformation("Successfully fetched {Count} projects", projectsResponse?.Count ?? 0);
            return projectsResponse?.Value ?? new List<ProjectDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while fetching projects from Azure DevOps");
            throw;
        }
    }

    /// <summary>
    /// Retrieves all work items for a specific project
    /// </summary>
    public async Task<IEnumerable<WorkItemDto>> GetWorkItemsAsync(string projectId)
    {
        try
        {
            _logger.LogInformation("Fetching work items for project {ProjectId}", projectId);

            // First, get work item IDs using WIQL (Work Item Query Language)
            var workItemIds = await GetWorkItemIdsAsync(projectId);
            
            if (!workItemIds.Any())
            {
                _logger.LogInformation("No work items found for project {ProjectId}", projectId);
                return new List<WorkItemDto>();
            }

            // Then get detailed work item information
            return await GetWorkItemDetailsAsync(workItemIds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while fetching work items for project {ProjectId}", projectId);
            throw;
        }
    }

    /// <summary>
    /// Gets work item IDs for a project using WIQL query
    /// </summary>
    private async Task<IEnumerable<int>> GetWorkItemIdsAsync(string projectId)
    {
        var wiqlQuery = new
        {
            query = $"SELECT [System.Id] FROM WorkItems WHERE [System.TeamProject] = '{projectId}'"
        };

        var requestUri = $"{projectId}/_apis/wit/wiql?api-version={_settings.ApiVersion}";
        var jsonContent = JsonSerializer.Serialize(wiqlQuery);
        var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

        HttpResponseMessage response;
        if (_settings.Authentication.Type.Equals("ClientCredentials", StringComparison.OrdinalIgnoreCase))
        {
            var request = await CreateAuthenticatedRequestAsync(HttpMethod.Post, requestUri);
            request.Content = content;
            response = await _httpClient.SendAsync(request);
        }
        else
        {
            response = await _httpClient.PostAsync(requestUri, content);
        }

        if (!response.IsSuccessStatusCode)
        {
            _logger.LogError("Failed to fetch work item IDs for project {ProjectId}. Status: {StatusCode}",
                projectId, response.StatusCode);
            throw new HttpRequestException($"Failed to query work items: {response.StatusCode}");
        }

        var responseContent = await response.Content.ReadAsStringAsync();
        var queryResult = JsonSerializer.Deserialize<JsonElement>(responseContent);

        var workItems = queryResult.GetProperty("workItems");
        var ids = new List<int>();

        foreach (var workItem in workItems.EnumerateArray())
        {
            if (workItem.TryGetProperty("id", out var idElement))
            {
                ids.Add(idElement.GetInt32());
            }
        }

        return ids;
    }

    /// <summary>
    /// Gets detailed work item information by IDs
    /// </summary>
    private async Task<IEnumerable<WorkItemDto>> GetWorkItemDetailsAsync(IEnumerable<int> workItemIds)
    {
        var ids = string.Join(",", workItemIds);
        var fields = "System.Id,System.Title,System.WorkItemType,System.State,System.AssignedTo,System.CreatedDate,System.ChangedDate";

        var requestUri = $"_apis/wit/workitems?ids={ids}&fields={fields}&api-version={_settings.ApiVersion}";

        HttpResponseMessage response;
        if (_settings.Authentication.Type.Equals("ClientCredentials", StringComparison.OrdinalIgnoreCase))
        {
            var request = await CreateAuthenticatedRequestAsync(HttpMethod.Get, requestUri);
            response = await _httpClient.SendAsync(request);
        }
        else
        {
            response = await _httpClient.GetAsync(requestUri);
        }

        if (!response.IsSuccessStatusCode)
        {
            _logger.LogError("Failed to fetch work item details. Status: {StatusCode}", response.StatusCode);
            throw new HttpRequestException($"Failed to fetch work item details: {response.StatusCode}");
        }

        var content = await response.Content.ReadAsStringAsync();
        var workItemsResponse = JsonSerializer.Deserialize<AzureDevOpsWorkItemsResponse>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        return workItemsResponse?.Value?.Select(MapToWorkItemDto) ?? new List<WorkItemDto>();
    }

    /// <summary>
    /// Retrieves a specific work item with all expanded details
    /// </summary>
    public async Task<WorkItemDetailDto> GetWorkItemAsync(int workItemId)
    {
        try
        {
            _logger.LogInformation("Fetching detailed work item {WorkItemId}", workItemId);

            // Get work item with expanded fields and relations
            var expand = "fields,relations";
            var requestUri = $"_apis/wit/workitems/{workItemId}?$expand={expand}&api-version={_settings.ApiVersion}";

            HttpResponseMessage response;
            if (_settings.Authentication.Type.Equals("ClientCredentials", StringComparison.OrdinalIgnoreCase))
            {
                var request = await CreateAuthenticatedRequestAsync(HttpMethod.Get, requestUri);
                response = await _httpClient.SendAsync(request);
            }
            else
            {
                response = await _httpClient.GetAsync(requestUri);
            }

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to fetch work item {WorkItemId}. Status: {StatusCode}",
                    workItemId, response.StatusCode);
                throw new HttpRequestException($"Failed to fetch work item: {response.StatusCode}");
            }

            var content = await response.Content.ReadAsStringAsync();
            var workItem = JsonSerializer.Deserialize<JsonElement>(content);

            _logger.LogInformation("Successfully fetched work item {WorkItemId}", workItemId);
            return MapToWorkItemDetailDto(workItem);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while fetching work item {WorkItemId}", workItemId);
            throw;
        }
    }

    /// <summary>
    /// Maps Azure DevOps work item JSON to detailed DTO
    /// </summary>
    private static WorkItemDetailDto MapToWorkItemDetailDto(JsonElement workItem)
    {
        var fields = workItem.GetProperty("fields");
        var id = workItem.GetProperty("id").GetInt32();
        var url = workItem.GetProperty("url").GetString() ?? string.Empty;

        var result = new WorkItemDetailDto
        {
            Id = id,
            Url = url,
            Title = GetFieldValue<string>(fields, "System.Title") ?? string.Empty,
            WorkItemType = GetFieldValue<string>(fields, "System.WorkItemType") ?? string.Empty,
            State = GetFieldValue<string>(fields, "System.State") ?? string.Empty,
            AssignedTo = GetUserDisplayName(fields, "System.AssignedTo"),
            CreatedBy = GetUserDisplayName(fields, "System.CreatedBy"),
            ChangedBy = GetUserDisplayName(fields, "System.ChangedBy"),
            CreatedDate = GetFieldValue<DateTime?>(fields, "System.CreatedDate") ?? DateTime.MinValue,
            ChangedDate = GetFieldValue<DateTime?>(fields, "System.ChangedDate") ?? DateTime.MinValue,
            Description = GetFieldValue<string>(fields, "System.Description"),
            AcceptanceCriteria = GetFieldValue<string>(fields, "Microsoft.VSTS.Common.AcceptanceCriteria"),
            Priority = GetFieldValue<int?>(fields, "Microsoft.VSTS.Common.Priority"),
            Severity = GetFieldValue<string>(fields, "Microsoft.VSTS.Common.Severity"),
            StoryPoints = GetFieldValue<double?>(fields, "Microsoft.VSTS.Scheduling.StoryPoints"),
            OriginalEstimate = GetFieldValue<double?>(fields, "Microsoft.VSTS.Scheduling.OriginalEstimate"),
            RemainingWork = GetFieldValue<double?>(fields, "Microsoft.VSTS.Scheduling.RemainingWork"),
            CompletedWork = GetFieldValue<double?>(fields, "Microsoft.VSTS.Scheduling.CompletedWork"),
            Tags = GetFieldValue<string>(fields, "System.Tags"),
            AreaPath = GetFieldValue<string>(fields, "System.AreaPath"),
            IterationPath = GetFieldValue<string>(fields, "System.IterationPath")
        };

        // Parse relations if they exist
        if (workItem.TryGetProperty("relations", out var relations))
        {
            result.Relations = ParseRelations(relations);
        }

        return result;
    }

    /// <summary>
    /// Helper method to get field values safely
    /// </summary>
    private static T? GetFieldValue<T>(JsonElement fields, string fieldName)
    {
        if (fields.TryGetProperty(fieldName, out var field))
        {
            try
            {
                if (typeof(T) == typeof(string))
                    return (T?)(object?)field.GetString();
                if (typeof(T) == typeof(int?) || typeof(T) == typeof(int))
                    return field.ValueKind == JsonValueKind.Number ? (T?)(object?)field.GetInt32() : default;
                if (typeof(T) == typeof(double?) || typeof(T) == typeof(double))
                    return field.ValueKind == JsonValueKind.Number ? (T?)(object?)field.GetDouble() : default;
                if (typeof(T) == typeof(DateTime?) || typeof(T) == typeof(DateTime))
                    return field.ValueKind == JsonValueKind.String ? (T?)(object?)field.GetDateTime() : default;
            }
            catch
            {
                // Return default if parsing fails
            }
        }
        return default;
    }

    /// <summary>
    /// Helper method to get user display name from user fields
    /// </summary>
    private static string? GetUserDisplayName(JsonElement fields, string fieldName)
    {
        if (fields.TryGetProperty(fieldName, out var userField))
        {
            if (userField.TryGetProperty("displayName", out var displayName))
            {
                return displayName.GetString();
            }
        }
        return null;
    }

    /// <summary>
    /// Parses work item relations
    /// </summary>
    private static List<WorkItemRelation> ParseRelations(JsonElement relations)
    {
        var result = new List<WorkItemRelation>();

        foreach (var relation in relations.EnumerateArray())
        {
            var rel = new WorkItemRelation
            {
                Rel = relation.TryGetProperty("rel", out var relProp) ? relProp.GetString() ?? string.Empty : string.Empty,
                Url = relation.TryGetProperty("url", out var urlProp) ? urlProp.GetString() ?? string.Empty : string.Empty
            };

            if (relation.TryGetProperty("attributes", out var attributes))
            {
                rel.Attributes = new Dictionary<string, object>();
                foreach (var attr in attributes.EnumerateObject())
                {
                    rel.Attributes[attr.Name] = attr.Value.ToString();
                }
            }

            result.Add(rel);
        }

        return result;
    }

    /// <summary>
    /// Maps Azure DevOps work item to DTO
    /// </summary>
    private static WorkItemDto MapToWorkItemDto(AzureDevOpsWorkItem azureWorkItem)
    {
        return new WorkItemDto
        {
            Id = azureWorkItem.Id,
            Title = azureWorkItem.Fields.Title ?? string.Empty,
            WorkItemType = azureWorkItem.Fields.WorkItemType ?? string.Empty,
            State = azureWorkItem.Fields.State ?? string.Empty,
            AssignedTo = azureWorkItem.Fields.AssignedTo?.DisplayName,
            CreatedDate = azureWorkItem.Fields.CreatedDate ?? DateTime.MinValue,
            ChangedDate = azureWorkItem.Fields.ChangedDate ?? DateTime.MinValue,
            Url = azureWorkItem.Url
        };
    }
}

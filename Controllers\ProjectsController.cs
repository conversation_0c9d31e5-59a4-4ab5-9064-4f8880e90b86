using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using TimeTrackingBackend.Models;
using TimeTrackingBackend.Services;

namespace TimeTrackingBackend.Controllers;

/// <summary>
/// Controller for managing Azure DevOps projects and work items
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class ProjectsController : ControllerBase
{
    private readonly IAzureDevOpsService _azureDevOpsService;
    private readonly ILogger<ProjectsController> _logger;

    public ProjectsController(
        IAzureDevOpsService azureDevOpsService,
        ILogger<ProjectsController> logger)
    {
        _azureDevOpsService = azureDevOpsService;
        _logger = logger;
    }

    /// <summary>
    /// Retrieves a list of all DevOps projects from Azure DevOps
    /// </summary>
    /// <returns>A list of projects with their details</returns>
    /// <response code="200">Returns the list of projects</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<ProjectDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<IEnumerable<ProjectDto>>> GetProjects()
    {
        try
        {
            _logger.LogInformation("Received request to get all projects");

            var projects = await _azureDevOpsService.GetProjectsAsync();

            _logger.LogInformation("Successfully retrieved {Count} projects", projects.Count());

            return Ok(projects);
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error occurred while fetching projects from Azure DevOps");
            return Problem(
                title: "Azure DevOps API Error",
                detail: "Failed to retrieve projects from Azure DevOps. Please check your configuration and try again.",
                statusCode: StatusCodes.Status502BadGateway);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error occurred while fetching projects");
            return Problem(
                title: "Internal Server Error",
                detail: "An unexpected error occurred while processing your request.",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Retrieves all work items from a specific DevOps project
    /// </summary>
    /// <param name="projectId">The unique identifier of the project</param>
    /// <returns>A list of work items for the specified project</returns>
    /// <response code="200">Returns the list of work items</response>
    /// <response code="400">If the project ID is invalid</response>
    /// <response code="404">If the project is not found</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet("{projectId}/workitems")]
    [ProducesResponseType(typeof(IEnumerable<WorkItemDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<IEnumerable<WorkItemDto>>> GetWorkItems(
        [FromRoute] [Required] string projectId)
    {
        try
        {
            // Validate project ID
            if (string.IsNullOrWhiteSpace(projectId))
            {
                _logger.LogWarning("Received request with empty or null project ID");
                return BadRequest(new ValidationProblemDetails(new Dictionary<string, string[]>
                {
                    { nameof(projectId), new[] { "Project ID cannot be empty or null" } }
                }));
            }

            // Additional validation for project ID format (basic validation)
            if (projectId.Length > 100 || projectId.Contains("..") || projectId.Contains("/"))
            {
                _logger.LogWarning("Received request with invalid project ID format: {ProjectId}", projectId);
                return BadRequest(new ValidationProblemDetails(new Dictionary<string, string[]>
                {
                    { nameof(projectId), new[] { "Project ID format is invalid" } }
                }));
            }

            _logger.LogInformation("Received request to get work items for project: {ProjectId}", projectId);

            var workItems = await _azureDevOpsService.GetWorkItemsAsync(projectId);

            _logger.LogInformation("Successfully retrieved {Count} work items for project {ProjectId}", 
                workItems.Count(), projectId);

            return Ok(workItems);
        }
        catch (HttpRequestException ex) when (ex.Message.Contains("404"))
        {
            _logger.LogWarning(ex, "Project not found: {ProjectId}", projectId);
            return NotFound(new ProblemDetails
            {
                Title = "Project Not Found",
                Detail = $"The project with ID '{projectId}' was not found.",
                Status = StatusCodes.Status404NotFound
            });
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error occurred while fetching work items for project {ProjectId}", projectId);
            return Problem(
                title: "Azure DevOps API Error",
                detail: "Failed to retrieve work items from Azure DevOps. Please check your configuration and try again.",
                statusCode: StatusCodes.Status502BadGateway);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error occurred while fetching work items for project {ProjectId}", projectId);
            return Problem(
                title: "Internal Server Error",
                detail: "An unexpected error occurred while processing your request.",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Retrieves a specific work item with all expanded details
    /// </summary>
    /// <param name="workItemId">The unique identifier of the work item</param>
    /// <returns>Detailed work item information with all fields and relations</returns>
    /// <response code="200">Returns the detailed work item</response>
    /// <response code="400">If the work item ID is invalid</response>
    /// <response code="404">If the work item is not found</response>
    /// <response code="500">If there was an internal server error</response>
    [HttpGet("workitems/{workItemId}")]
    [ProducesResponseType(typeof(WorkItemDetailDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<WorkItemDetailDto>> GetWorkItem(
        [FromRoute] [Required] int workItemId)
    {
        try
        {
            // Validate work item ID
            if (workItemId <= 0)
            {
                _logger.LogWarning("Received request with invalid work item ID: {WorkItemId}", workItemId);
                return BadRequest(new ValidationProblemDetails(new Dictionary<string, string[]>
                {
                    { nameof(workItemId), new[] { "Work item ID must be a positive integer" } }
                }));
            }

            _logger.LogInformation("Received request to get work item: {WorkItemId}", workItemId);

            var workItem = await _azureDevOpsService.GetWorkItemAsync(workItemId);

            _logger.LogInformation("Successfully retrieved work item {WorkItemId}", workItemId);

            return Ok(workItem);
        }
        catch (HttpRequestException ex) when (ex.Message.Contains("404"))
        {
            _logger.LogWarning(ex, "Work item not found: {WorkItemId}", workItemId);
            return NotFound(new ProblemDetails
            {
                Title = "Work Item Not Found",
                Detail = $"The work item with ID '{workItemId}' was not found.",
                Status = StatusCodes.Status404NotFound
            });
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error occurred while fetching work item {WorkItemId}", workItemId);
            return Problem(
                title: "Azure DevOps API Error",
                detail: "Failed to retrieve work item from Azure DevOps. Please check your configuration and try again.",
                statusCode: StatusCodes.Status502BadGateway);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error occurred while fetching work item {WorkItemId}", workItemId);
            return Problem(
                title: "Internal Server Error",
                detail: "An unexpected error occurred while processing your request.",
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }
}
